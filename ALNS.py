import random
import math
import copy
from typing import Dict, Tuple
from scipy.stats import norm
from base import Solution, initialize_operators, apply_2opt, apply_swap_1_1


class ALNSController:
    def __init__(self, problem, initial_solution, config: Dict):
        """
        Initialize the ALNS controller for the stochastic IRP.

        Args:
            problem: The problem instance.
            initial_solution: The initial solution to start with.
            config (Dict): Configuration parameters for ALNS.
        """
        self.problem = problem
        self.iterations = config.get("iterations", 1000)
        self.start_temperature = config.get("start_temperature", 1000.0)
        self.cooling_rate = config.get("cooling_rate", 0.995)
        self.min_temperature = config.get("min_temperature", 0.1)
        self.removal_count_min = config.get("removal_count_min", 1)
        self.removal_count_max = config.get("removal_count_max", 3)
        self.weight_update_frequency = config.get("weight_update_frequency", 100)
        self.weight_decay = config.get("weight_decay", 0.9)
        self.acceptance_scores = config.get("acceptance_scores", [33, 22, 11, 0])

        # Initialize operators
        self.removal_operators, self.insertion_operators, self.operator_weights = initialize_operators(problem)

        # Use the provided initial solution
        self.best_solution = initial_solution
        self.current_solution = initial_solution
        self.best_cost = self.best_solution.calculate_cost()

    def select_operator(self, operator_type: str) -> Tuple[str, object]:
        """Select an operator based on weights."""
        operators = self.removal_operators if operator_type == 'removal' else self.insertion_operators
        weights = self.operator_weights[operator_type]
        total_weight = sum(weights.values())
        r = random.uniform(0, total_weight)
        cum_weight = 0
        for name, weight in weights.items():
            cum_weight += weight
            if r <= cum_weight:
                return name, operators[name]
        return list(operators.items())[-1]  # Fallback

    def accept_solution(self, new_cost: float, current_cost: float, temperature: float) -> bool:
        """Determine if a new solution is accepted using simulated annealing."""
        if new_cost <= current_cost:
            return True
        delta = new_cost - current_cost
        return random.random() < math.exp(-delta / temperature)

    def update_operator_weights(self, operator_name: str, operator_type: str, score: int):
        """Update operator weights based on performance."""
        weights = self.operator_weights[operator_type]
        weights[operator_name] = self.weight_decay * weights[operator_name] + (1 - self.weight_decay) * score

    def run(self) -> Solution:
        """
        Run the ALNS algorithm to optimize the stochastic IRP solution.

        Returns:
            Solution: The best solution found.
        """
        current_solution = self.current_solution
        best_solution = self.best_solution
        best_cost = self.best_cost
        temperature = self.start_temperature

        for iteration in range(self.iterations):
            # Select operators
            removal_name, removal_op = self.select_operator('removal')
            insertion_name, insertion_op = self.select_operator('insertion')

            # Create a deep copy of the current solution
            new_solution = copy.deepcopy(current_solution)

            # Apply removal
            removal_count = random.randint(self.removal_count_min, self.removal_count_max)
            removed_ports = removal_op.apply(new_solution, 0, removal_count)

            # Apply insertion
            insertion_op.apply(new_solution, 0)

            # --- Start Local Search Integration ---
            improved_by_ls = True
            while improved_by_ls:  # Keep applying LS until no further improvement
                # Important: Apply LS directly to new_solution
                ls1_improved = apply_2opt(new_solution)
                # Recalculate cost before potentially applying swap if 2opt improved solution structure
                if ls1_improved:
                    _ = new_solution.calculate_cost()  # Ensure inventory_mean is updated after 2opt
                ls2_improved = apply_swap_1_1(new_solution)
                improved_by_ls = ls1_improved or ls2_improved
                # Ensure cost reflects final state after LS loop for hard constraint check below
                if improved_by_ls:
                    _ = new_solution.calculate_cost()  # Ensure inventory_mean is updated
            # --- End Local Search Integration ---

            # Evaluate new solution
            new_cost = new_solution.total_cost  # Use the stored cost after LS
            current_cost = current_solution.calculate_cost()  # Recalculate for safety

            # Check hard constraints on inventory levels
            if hasattr(new_solution, 'inventory_mean'):
                # Calculate confidence interval parameters
                rh = 1.0 - self.problem.confidence_level  # e.g., 0.1
                alpha_L = rh / 2.0  # Violation probability for lower bound
                alpha_U = rh / 2.0  # Violation probability for upper bound
                # Z-score for lower bound (P(I < L) <= alpha_L)
                tau = norm.ppf(alpha_L)  # Should be negative, e.g., -1.645
                # Z-score for upper bound (P(I > U) <= alpha_U => P(I <= U) >= 1-alpha_U)
                zeta = norm.ppf(1.0 - alpha_U)  # Should be positive, e.g., +1.645

                # Check each port and day for constraint violations
                for port in range(1, self.problem.N):
                    if port in new_solution.inventory_mean:
                        port_info = self.problem.ports[port]
                        lower_limit = port_info['inventory_lower_limit']
                        upper_limit = port_info['inventory_upper_limit']

                        for day in range(1, min(len(new_solution.inventory_mean[port]), self.problem.T + 1)):
                            inventory_mean = new_solution.inventory_mean[port][day]
                            sigma_demand = port_info['sigma_demand'][day-1]  # Use demand sigma for the period *leading* to this inventory state

                            # S&A Eq 27 check: Mh >= SLm - tau * sigma
                            lower_bound_check = lower_limit - tau * sigma_demand  # Note: -tau is positive
                            if inventory_mean < lower_bound_check:
                                new_cost = float('inf')  # Reject solution
                                break

                            # S&A Eq 28 check: Mh <= SLM - zeta * sigma
                            upper_bound_check = upper_limit - zeta * sigma_demand  # Note: -zeta is negative
                            if inventory_mean > upper_bound_check:
                                new_cost = float('inf')  # Reject solution
                                break

            # Determine acceptance
            score = 0
            if new_cost < best_cost:
                best_solution = new_solution
                best_cost = new_cost
                current_solution = new_solution
                score = self.acceptance_scores[0]
            elif new_cost < current_cost:
                current_solution = new_solution
                score = self.acceptance_scores[1]
            elif self.accept_solution(new_cost, current_cost, temperature):
                current_solution = new_solution
                score = self.acceptance_scores[2]
            else:
                score = self.acceptance_scores[3]

            # Update operator weights
            if iteration % self.weight_update_frequency == 0 and iteration > 0:
                self.update_operator_weights(removal_name, 'removal', score)
                self.update_operator_weights(insertion_name, 'insertion', score)

            # Update temperature
            temperature = max(temperature * self.cooling_rate, self.min_temperature)

        self.best_solution = best_solution
        self.best_cost = best_cost
        return best_solution