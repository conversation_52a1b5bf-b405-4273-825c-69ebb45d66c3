import numpy as np
import math
import random
import copy
from typing import List, Dict, Tuple
from scipy.stats import norm


class Problem:
    def __init__(self, T: int = 25):
        """
        Initialize the stochastic IRP problem with a single 25-day planning horizon.

        Args:
            T (int): Planning horizon in days, default is 25.
        """
        self.T = T  # Planning horizon: 25 days
        self.N = 7  # Number of ports (0 supply, 1-6 consumption)
        self.ports = self.initialize_ports()
        self.vessel_types = [
            {"capacity": 35000, "travel_time": self.initialize_travel_time_35000(),
             "travel_cost": self.initialize_travel_cost_35000()},
            {"capacity": 45000, "travel_time": self.initialize_travel_time_45000(),
             "travel_cost": self.initialize_travel_cost_45000()}
        ]
        self.unit_loading_cost = 0.1
        self.unit_holding_cost = 0.05
        self.penalty_type_I = 18  # Penalty for violating lower inventory limit
        self.penalty_type_II = 16  # Penalty for violating upper inventory limit
        self.confidence_level = 0.9  # 90% confidence for chance constraints

        # Precompute safety stock and expected penalties
        self.safety_stock = self.compute_safety_stock()
        self.expected_penalties = self.compute_expected_penalties()

    def initialize_ports(self) -> List[Dict]:
        """
        Initialize port data with stochastic demand parameters (mean and variance) for consumption ports.

        Returns:
            List[Dict]: List of port dictionaries containing inventory limits and demand statistics.
        """
        # Demand mean and variance ranges from the document
        demand_ranges = [
            (float('inf'), 0),  # Port 0: Supply (no demand)
            (950, 1080, 240, 270),  # Port 1
            (1160, 1280, 300, 330),  # Port 2
            (980, 1135, 250, 280),  # Port 3
            (860, 950, 210, 230),  # Port 4
            (1200, 1300, 290, 310),  # Port 6
            (840, 905, 210, 230)  # Port 7
        ]

        ports = [
            {"production_rate": float('inf')},  # Port 0: Supply port, no inventory or demand
            {"initial_inventory": 2.73, "inventory_lower_limit": 1.49, "inventory_upper_limit": 3.5},
            {"initial_inventory": 2.65, "inventory_lower_limit": 1.35, "inventory_upper_limit": 3.69},
            {"initial_inventory": 3.05, "inventory_lower_limit": 1.44, "inventory_upper_limit": 3.25},
            {"initial_inventory": 2.53, "inventory_lower_limit": 1.12, "inventory_upper_limit": 2.63},
            {"initial_inventory": 3.33, "inventory_lower_limit": 1.86, "inventory_upper_limit": 5.43},
            {"initial_inventory": 2.45, "inventory_lower_limit": 1.29, "inventory_upper_limit": 2.98}
        ]

        # Add stochastic demand parameters for consumption ports (1-4, 6-7)
        for i in range(1, self.N):
            mu_min, mu_max, sigma_min, sigma_max = demand_ranges[i]
            ports[i]["mu_demand"] = [random.uniform(mu_min, mu_max) for _ in range(self.T)]
            ports[i]["sigma_demand"] = [random.uniform(sigma_min, sigma_max) for _ in range(self.T)]

        return ports

    def compute_safety_stock(self) -> List[List[float]]:
        """
        Compute safety stock thresholds for each consumption port and time period based on chance constraints.
        Implements the opportunity constraints:
        P(I_t ≥ S_min) ≥ confidence_level
        P(I_t ≤ S_max) ≥ confidence_level

        Returns:
            List[List[float]]: Safety stock levels for each port (1-6) and time period.
        """
        safety_stock = []
        z_value = norm.ppf(self.confidence_level)  # z-score for one-sided confidence level

        for i in range(1, self.N):
            port = self.ports[i]
            safety_stock_i = []
            for t in range(self.T):
                mu = port["mu_demand"][t]
                sigma = port["sigma_demand"][t]
                lower_limit = port["inventory_lower_limit"]
                upper_limit = port["inventory_upper_limit"]

                # Target inventory level to satisfy both constraints
                # For lower limit: inventory_mean - z_value*sigma ≥ lower_limit
                # This means: inventory_mean ≥ lower_limit + z_value*sigma
                min_target = lower_limit + z_value * sigma

                # For upper limit: inventory_mean + z_value*sigma ≤ upper_limit
                # This means: inventory_mean ≤ upper_limit - z_value*sigma
                max_target = upper_limit - z_value * sigma

                # Target the middle of the feasible range
                target_level = (min_target + max_target) / 2

                # Ensure the target is feasible
                if min_target > max_target:
                    # If constraints can't be satisfied simultaneously, prioritize lower limit
                    target_level = min_target

                # Add expected demand to get the safety stock level
                safety_stock_level = target_level + mu

                safety_stock_i.append(max(0, safety_stock_level))  # Ensure non-negative
            safety_stock.append(safety_stock_i)

        return safety_stock

    def compute_expected_penalties(self) -> List[List[tuple]]:
        """
        Precompute expected Type I and Type II penalties for each port and time period.

        Returns:
            List[List[tuple]]: List of (expected_type_I, expected_type_II) tuples for each port and time period.
        """
        expected_penalties = []

        for i in range(1, self.N):
            port = self.ports[i]
            penalties_i = []
            for t in range(self.T):
                mu = port["mu_demand"][t]
                sigma = port["sigma_demand"][t]
                lower_limit = port["inventory_lower_limit"]
                upper_limit = port["inventory_upper_limit"]

                # Expected Type I penalty: E[max(0, lower_limit - I)]
                # For inventory level I with mean mu_I and std sigma
                # We use a simplified approach for expected penalties
                type_I = self.penalty_type_I * max(0, lower_limit - mu) * 0.5

                # Expected Type II penalty: E[max(0, I - upper_limit)]
                type_II = self.penalty_type_II * max(0, mu - upper_limit) * 0.5

                penalties_i.append((type_I, type_II))
            expected_penalties.append(penalties_i)

        return expected_penalties

    def initialize_travel_time_35000(self) -> List[List[int]]:
        """Travel times for vessel with capacity 35000 (in days)."""
        return [
            [0, 4, 3, 5, 7, 7, 2],
            [4, 0, 2, 1, 3, 3, 3],
            [3, 2, 0, 2, 3, 4, 2],
            [5, 1, 2, 0, 2, 2, 3],
            [7, 3, 3, 2, 0, 1, 4],
            [7, 3, 4, 2, 1, 0, 4],
            [2, 3, 2, 3, 4, 4, 0]
        ]

    def initialize_travel_cost_35000(self) -> List[List[float]]:
        """Travel costs for vessel with capacity 35000."""
        return [
            [0, 34894, 34148, 35184, 35886, 35952, 33719],
            [34894, 0, 33380, 33056, 33758, 33814, 33954],
            [34148, 33380, 0, 33670, 34362, 34438, 33208],
            [35184, 33056, 33670, 0, 33431, 33497, 34244],
            [35886, 33758, 34362, 33431, 0, 32697, 34935],
            [35952, 33814, 34438, 33497, 32697, 0, 34423],
            [33719, 33954, 33208, 34244, 34935, 34423, 0]
        ]

    def initialize_travel_time_45000(self) -> List[List[int]]:
        """Travel times for vessel with capacity 45000 (in days)."""
        return [
            [0, 4, 3, 5, 6, 6, 2],
            [4, 0, 2, 1, 2, 3, 3],
            [3, 2, 0, 2, 3, 3, 2],
            [5, 1, 2, 0, 2, 2, 3],
            [6, 2, 3, 2, 0, 1, 4],
            [6, 3, 3, 2, 1, 0, 4],
            [2, 3, 2, 3, 4, 4, 0]
        ]

    def initialize_travel_cost_45000(self) -> List[List[float]]:
        """Travel costs for vessel with capacity 45000."""
        return [
            [0, 41394, 40648, 41684, 42376, 42442, 40219],
            [41394, 0, 39880, 39556, 40248, 40314, 40454],
            [40648, 39880, 0, 40170, 40862, 40938, 39708],
            [41684, 39556, 40170, 0, 39931, 39997, 40744],
            [42376, 40248, 40862, 39931, 0, 39197, 41435],
            [42442, 40314, 40938, 39997, 39197, 0, 41502],
            [40219, 40454, 39708, 40744, 41435, 41502, 0]
        ]

    def calculate_cycle_travel_cost(self, route: List[int], vessel_type: int) -> float:
        """Calculate travel cost for a route using the specified vessel type."""
        cost = 0
        for i in range(len(route) - 1):
            cost += self.vessel_types[vessel_type]["travel_cost"][route[i]][route[i + 1]]
        return cost

class Solution:
    def __init__(self, problem):
        """
        Initialize a solution for the stochastic IRP with a single 25-day planning period.

        Args:
            problem (Problem): The problem instance containing port and vessel data.
        """
        self.problem = problem

        # Initialize data structures for the 25-day planning period
        self.routes = []  # List of routes for each vessel
        self.vessel_types = []  # Vessel type for each route
        self.arrival_times = []  # Arrival times at each port
        self.departure_times = []  # Departure times from each port
        self.loading_amounts = []  # Loading amounts at each port
        self.port_visits = {  # Track visits for each port
            i: {'visit_days': [], 'deliveries': [], 'vessel_types': []}
            for i in range(problem.N)
        }

        # Track vessels and their schedules
        self.vessels = [
            {
                'type': vessel_type,
                'capacity': problem.vessel_types[vessel_type]['capacity'],
                'current_load': 0,
                'current_location': 0,  # All vessels start at port 0
                'current_time': 0,
                'available_time': 0,  # Time when vessel becomes available for next task
                'schedule': [],  # List of events (port, arrival_time, departure_time, load_change)
                'route': []  # Complete route of the vessel
            }
            for vessel_type in range(len(problem.vessel_types))
        ]

        self.unvisited_ports = list(range(1, problem.N))

        # Track inventory levels at each port over time (25 days)
        self.inventory_levels = {
            port: [problem.ports[port]['initial_inventory'] if port > 0 else float('inf')]
            for port in range(problem.N)
        }

        self.total_cost = float('inf')

    def calculate_expected_inventory_cost(self, port: int) -> float:
        """
        Calculate expected inventory cost for a port over the entire 25-day planning period.
        Handles multi-day travel times and loading/unloading activities.

        Args:
            port (int): Port index (1-6 for consumption ports).

        Returns:
            float: Expected total cost including holding costs and penalties over 25 days.
        """
        if port == 0:
            return 0  # Supply port has no inventory costs

        port_info = self.problem.ports[port]
        inventory_mean = [port_info["initial_inventory"]]  # Start with initial inventory
        total_cost = 0

        # Get all visits to this port across all vessels
        all_visits = []
        for vessel in self.vessels:
            for event in vessel['schedule']:
                if event['port'] == port and event['load_change'] < 0:  # Unloading event
                    all_visits.append({
                        'day': event['arrival_time'],
                        'delivery': -event['load_change']  # Convert to positive
                    })

        # Sort visits by day
        all_visits.sort(key=lambda x: x['day'])

        # Simulate inventory changes day by day for the entire planning period
        for day in range(self.problem.T):
            # Current inventory is the last element in inventory_mean
            inventory = inventory_mean[-1]

            # Apply deliveries for visits that occur on this day
            for visit in all_visits:
                if day == int(visit['day']):
                    inventory += visit['delivery']
                    total_cost += visit['delivery'] * self.problem.unit_loading_cost * 2  # Loading + unloading

            # Expected inventory after demand
            mu_d = port_info["mu_demand"][day]
            expected_inventory = inventory - mu_d

            # Holding cost: max(0, inventory_mean) * unit_holding_cost
            holding_cost = max(0, expected_inventory) * self.problem.unit_holding_cost
            total_cost += holding_cost

            # Add pre-calculated expected penalties
            type_I_penalty = self.problem.expected_penalties[port-1][day][0]
            type_II_penalty = self.problem.expected_penalties[port-1][day][1]
            total_cost += type_I_penalty + type_II_penalty

            # Store inventory level for next day
            inventory_mean.append(expected_inventory)

        # Store the inventory mean array for this port
        self.inventory_mean = {} if not hasattr(self, 'inventory_mean') else self.inventory_mean
        self.inventory_mean[port] = inventory_mean

        return total_cost

    def get_final_inventory(self, port: int, day: int = -1) -> float:
        """
        Estimate the expected inventory for a port at the specified day.
        If day is -1, returns the current inventory level.

        Args:
            port (int): Port index.
            day (int): Day index (0-24), or -1 for current inventory.

        Returns:
            float: Expected inventory level.
        """
        if port == 0:
            return float('inf')  # Supply port

        port_info = self.problem.ports[port]

        if day == -1:
            # Return current inventory (initial if no simulation has been done)
            if port in self.inventory_levels and len(self.inventory_levels[port]) > 0:
                return self.inventory_levels[port][-1]
            else:
                return port_info["initial_inventory"]

        # If we have already simulated up to this day, return the stored value
        if port in self.inventory_levels and len(self.inventory_levels[port]) > day + 1:
            return self.inventory_levels[port][day + 1]

        # Otherwise, we need to simulate up to this day
        inventory = port_info["initial_inventory"]

        # Get all visits to this port
        all_visits = []
        for vessel in self.vessels:
            for event in vessel['schedule']:
                if event['port'] == port and event['load_change'] < 0:  # Unloading event
                    all_visits.append({
                        'day': event['arrival_time'],
                        'delivery': -event['load_change']  # Convert to positive
                    })

        # Sort visits by day
        all_visits.sort(key=lambda x: x['day'])

        # Simulate up to the requested day
        for d in range(min(day + 1, self.problem.T)):
            # Apply deliveries for visits that occur on this day
            for visit in all_visits:
                if d == int(visit['day']):
                    inventory += visit['delivery']

            # Subtract daily demand
            inventory -= port_info["mu_demand"][d]

        return inventory

    def validate_visits(self, port: int, vessel_type: int) -> bool:
        """
        Validate if a port can be visited with the given vessel type.
        Each port can be visited at most twice, and if visited twice, it must be by different vessel types.

        Args:
            port (int): Port index.
            vessel_type (int): Vessel type index (0 for 35000, 1 for 45000).

        Returns:
            bool: True if visit is valid, False otherwise.
        """
        if port == 0:
            return True  # Supply port can always be visited

        # Check how many times this port has been visited
        visit_count = len(self.port_visits[port]['visit_days'])

        if visit_count >= 2:
            return False  # Already visited twice

        if visit_count == 1:
            # Check if the vessel type is different from the existing visit
            existing_vessel = self.port_visits[port]['vessel_types'][0]
            if existing_vessel == vessel_type:
                return False  # Same vessel type, not allowed for second visit

        return True

    def validate_route(self, route: List[int], vessel_type: int) -> bool:
        """
        Validate if a route is feasible for the given vessel type.
        Accounts for multi-day travel times and 1-day loading/unloading activities.
        Ensures each port is visited at most twice, and if twice, by different vessel types.

        Args:
            route (List[int]): List of port indices in the route.
            vessel_type (int): Vessel type index.

        Returns:
            bool: True if route is valid, False otherwise.
        """
        if not route:
            return True

        # Check if we have enough days in the planning period for this route
        current_time = 0
        current_load = self.problem.vessel_types[vessel_type]["capacity"]

        # Check if all ports in the route can be visited
        for i, port in enumerate(route):
            if i > 0:
                # Add travel time from previous port
                travel_time = self.problem.vessel_types[vessel_type]["travel_time"][route[i - 1]][port]
                current_time += travel_time

                # Add 1 day for loading/unloading at each port (except the final return to supply port)
                if port != 0 or i < len(route) - 1:
                    current_time += 1  # 1 day for loading/unloading

            # Check if we're still within the planning period
            if current_time >= self.problem.T:
                return False

            if port != 0:  # Consumption port
                # Check visit constraints (max 2 visits, different vessel types)
                if not self.validate_visits(port, vessel_type):
                    return False

                # Check if we have enough capacity for the delivery
                safety_stock = self.problem.safety_stock[port - 1][0]  # Use first day as reference
                if safety_stock > current_load:
                    return False

                current_load -= safety_stock  # Assume minimum delivery

        return True

    def add_route(self, route: List[int], vessel_type: int) -> bool:
        """
        Add a route to the solution if feasible, and update vessel schedules.

        Args:
            route (List[int]): List of port indices.
            vessel_type (int): Vessel type index.

        Returns:
            bool: True if route was added, False otherwise.
        """
        if not self.validate_route(route, vessel_type):
            return False

        # Find the vessel with the earliest available time
        vessel_idx = -1
        earliest_time = float('inf')
        for i, vessel in enumerate(self.vessels):
            if vessel['type'] == vessel_type and vessel['available_time'] < earliest_time:
                vessel_idx = i
                earliest_time = vessel['available_time']

        if vessel_idx == -1:
            return False  # No vessel of this type available

        vessel = self.vessels[vessel_idx]
        current_time = vessel['available_time']
        current_location = vessel['current_location']
        current_load = vessel['current_load']

        # Calculate arrival and departure times for each port
        arrival_times = []
        departure_times = []
        loading_amounts = []

        for i, port in enumerate(route):
            # Calculate travel time from previous location
            if i == 0 and port == 0:
                # Starting at supply port
                arrival_time = current_time
            else:
                prev_port = current_location if i == 0 else route[i-1]
                travel_time = self.problem.vessel_types[vessel_type]['travel_time'][prev_port][port]
                arrival_time = current_time + travel_time

            arrival_times.append(arrival_time)

            # Calculate loading/unloading time and amount
            if port == 0:  # Supply port
                # Calculate how much to load based on the next ports in the route
                load_amount = 0
                for next_port in route[i+1:]:
                    if next_port != 0:  # Skip supply port
                        # Calculate demand for this port based on target inventory level
                        port_info = self.problem.ports[next_port]

                        # Simulate inventory up to arrival time at the next port
                        inventory_at_arrival = port_info["initial_inventory"]
                        arrival_day_int = int(arrival_time)

                        # Subtract expected demand
                        for d in range(min(arrival_day_int, self.problem.T)):
                            inventory_at_arrival -= port_info["mu_demand"][d]

                        # Add prior deliveries
                        for v_check in self.vessels:  # Check all vessels
                            for ev in v_check['schedule']:
                                if ev['port'] == next_port and ev['arrival_time'] < arrival_time and ev['load_change'] < 0:
                                    inventory_at_arrival -= ev['load_change']  # Add delivered amount

                        # Calculate target level using safety stock for the arrival day
                        target_level = self.problem.safety_stock[next_port - 1][min(arrival_day_int, self.problem.T-1)]

                        # Calculate how much to deliver
                        Q_needed = max(0, target_level - inventory_at_arrival)

                        # Ensure we don't exceed upper inventory limit
                        max_allowed = port_info["inventory_upper_limit"] - inventory_at_arrival
                        delivery_amount = min(Q_needed, max_allowed)

                        load_amount += delivery_amount

                # Ensure we don't exceed vessel capacity
                load_amount = min(load_amount, vessel['capacity'] - current_load)
                loading_time = 1  # 1 day for loading
                loading_amounts.append(load_amount)
                current_load += load_amount
            else:  # Consumption port
                # Calculate how much to unload based on port's needs
                port_info = self.problem.ports[port]

                # Simulate inventory up to arrival time
                inventory_at_arrival = port_info["initial_inventory"]
                arrival_day_int = int(arrival_time)

                # Subtract expected demand
                for d in range(min(arrival_day_int, self.problem.T)):
                    inventory_at_arrival -= port_info["mu_demand"][d]

                # Add prior deliveries
                for v_check in self.vessels:  # Check all vessels
                    for ev in v_check['schedule']:
                        if ev['port'] == port and ev['arrival_time'] < arrival_time and ev['load_change'] < 0:
                            inventory_at_arrival -= ev['load_change']  # Add delivered amount

                # Calculate target level using safety stock for the arrival day
                target_level = self.problem.safety_stock[port - 1][min(arrival_day_int, self.problem.T-1)]

                # Calculate how much to deliver
                Q_needed = max(0, target_level - inventory_at_arrival)

                # Ensure we don't exceed upper inventory limit
                max_allowed = port_info["inventory_upper_limit"] - inventory_at_arrival
                unload_amount = min(Q_needed, max_allowed)

                # Ensure we don't unload more than we have on board
                unload_amount = min(unload_amount, current_load)
                loading_time = 1  # 1 day for unloading
                loading_amounts.append(-unload_amount)  # Negative for unloading
                current_load -= unload_amount

            departure_time = arrival_time + loading_time
            departure_times.append(departure_time)
            current_time = departure_time
            current_location = port

            # Update vessel schedule
            vessel['schedule'].append({
                'port': port,
                'arrival_time': arrival_time,
                'departure_time': departure_time,
                'load_change': loading_amounts[-1]
            })

            # Update inventory levels at the port
            if port != 0:  # Skip supply port
                # Record the visit day
                visit_day = int(arrival_time)
                self.port_visits[port]['visit_days'].append(visit_day)
                self.port_visits[port]['deliveries'].append(-loading_amounts[-1])  # Convert to positive
                self.port_visits[port]['vessel_types'].append(vessel_type)
                if port in self.unvisited_ports:
                    self.unvisited_ports.remove(port)

        # Update vessel state
        vessel['current_location'] = route[-1]
        vessel['current_time'] = departure_times[-1]
        vessel['current_load'] = current_load
        vessel['available_time'] = departure_times[-1]
        vessel['route'].extend(route)

        # Update solution data structures
        self.routes.append(route)
        self.vessel_types.append(vessel_type)
        self.arrival_times.append(arrival_times)
        self.departure_times.append(departure_times)
        self.loading_amounts.append(loading_amounts)

        return True

    def calculate_arrival_times(self, route: List[int], vessel_type: int) -> List[float]:
        """
        Calculate arrival times for each port in a route, accounting for travel time and loading/unloading time.

        Args:
            route (List[int]): List of port indices.
            vessel_type (int): Vessel type index.

        Returns:
            List[float]: Arrival times at each port.
        """
        arrival_times = [0]  # Start time at first port
        current_time = 0

        for i in range(1, len(route)):
            # Add loading/unloading time at previous port (1 day)
            # Skip for the initial supply port if it's just a starting point
            if i > 1 or route[0] != 0:
                current_time += 1

            # Add travel time to next port
            current_time += self.problem.vessel_types[vessel_type]["travel_time"][route[i - 1]][route[i]]
            arrival_times.append(current_time)

        return arrival_times

    def calculate_cost(self) -> float:
        """
        Calculate the total expected cost of the solution over the entire planning period.

        Returns:
            float: Total expected cost including travel, handling, holding, and penalties.
        """
        # Calculate travel costs for all vessels
        travel_cost = 0
        handling_cost = 0

        for vessel in self.vessels:
            vessel_type = vessel['type']
            current_loc = 0  # Start at depot

            # Calculate travel costs based on schedule
            for event in vessel['schedule']:
                to_port = event['port']
                # Add cost only if moving between different ports
                if current_loc != to_port:
                    travel_cost += self.problem.vessel_types[vessel_type]['travel_cost'][current_loc][to_port]
                current_loc = to_port  # Update location for the start of the next leg

            # Optionally, add cost to return to depot 0 if not already there at the end
            # if current_loc != 0:
            #     travel_cost += self.problem.vessel_types[vessel_type]['travel_cost'][current_loc][0]

            # Calculate handling costs
            for event in vessel['schedule']:
                handling_cost += abs(event['load_change']) * self.problem.unit_loading_cost

        # Calculate inventory costs for all ports
        inventory_cost = 0
        self.inventory_mean = {}  # Reset inventory mean tracking

        for port in range(1, self.problem.N):
            inventory_cost += self.calculate_expected_inventory_cost(port)

        # Calculate total cost
        total_cost = travel_cost + handling_cost + inventory_cost

        self.total_cost = total_cost
        return total_cost

class RemovalOperator:
    """Base class for removal operators."""

    def __init__(self, problem):
        self.problem = problem

    def get_removable_positions(self, solution: Solution, cycle: int = 0) -> List[Tuple[int, int]]:
        """Get list of removable positions (route_idx, position) in the solution."""
        removable = []
        for route_idx, route in enumerate(solution.routes):
            for pos in range(1, len(route)):  # Skip supply port (position 0)
                if route[pos] != 0:  # Only remove consumption ports
                    removable.append((route_idx, pos))
        return removable


class RandomRemoval(RemovalOperator):
    """Random removal operator."""

    def apply(self, solution: Solution, cycle: int = 0, removal_count: int = 1) -> List[int]:
        """Remove random ports from routes."""
        removable_positions = self.get_removable_positions(solution)
        if not removable_positions:
            return []

        removal_count = min(removal_count, len(removable_positions))
        positions_to_remove = random.sample(removable_positions, removal_count)
        removed_ports = []

        # Create a list of ports to remove and routes to check
        ports_to_remove = []
        for route_idx, pos in positions_to_remove:
            if route_idx < len(solution.routes):
                route = solution.routes[route_idx]
                if pos < len(route):
                    port = route[pos]
                    ports_to_remove.append((route_idx, pos, port))

        # Sort in reverse order to avoid index issues
        ports_to_remove.sort(key=lambda x: (x[0], x[1]), reverse=True)

        # Remove ports
        routes_to_remove = []
        for route_idx, pos, port in ports_to_remove:
            route = solution.routes[route_idx]
            route.pop(pos)
            removed_ports.append(port)

            # Update port visits
            if port in solution.port_visits and solution.port_visits[port]['visit_days']:
                solution.port_visits[port]['visit_days'].pop()
                solution.port_visits[port]['deliveries'].pop()
                solution.port_visits[port]['vessel_types'].pop()

            if port not in solution.unvisited_ports:
                solution.unvisited_ports.append(port)

            # Mark empty routes for removal
            if len(route) <= 1:
                routes_to_remove.append(route_idx)

        # Remove empty routes in reverse order
        for route_idx in sorted(set(routes_to_remove), reverse=True):
            if route_idx < len(solution.routes):
                solution.routes.pop(route_idx)
                solution.vessel_types.pop(route_idx)
                solution.arrival_times.pop(route_idx)
                if len(solution.departure_times) > route_idx:
                    solution.departure_times.pop(route_idx)
                if len(solution.loading_amounts) > route_idx:
                    solution.loading_amounts.pop(route_idx)

        return removed_ports


class RelatedRemoval(RemovalOperator):
    """Removal operator based on port relatedness."""

    def apply(self, solution: Solution, cycle: int = 0, removal_count: int = 1) -> List[int]:
        """Remove related ports based on distance and demand variance."""
        removable_positions = self.get_removable_positions(solution)
        if not removable_positions:
            return []

        # Validate positions
        valid_positions = []
        for route_idx, pos in removable_positions:
            if route_idx < len(solution.routes):
                route = solution.routes[route_idx]
                if pos < len(route) and route[pos] != 0:
                    valid_positions.append((route_idx, pos))

        if not valid_positions:
            return []

        # Select initial port
        route_idx, pos = random.choice(valid_positions)
        route = solution.routes[route_idx]
        initial_port = route[pos]

        # Collect ports to remove
        ports_to_remove = [(route_idx, pos, initial_port)]
        valid_positions.remove((route_idx, pos))

        while len(ports_to_remove) < removal_count and valid_positions:
            best_relatedness = float('-inf')
            best_removal = None

            for route_idx, pos in valid_positions:
                route = solution.routes[route_idx]
                port = route[pos]
                relatedness = self._calculate_relatedness(initial_port, port, 0)  # Use 0 as cycle

                if relatedness > best_relatedness:
                    best_relatedness = relatedness
                    best_removal = (route_idx, pos, port)

            if best_removal:
                route_idx, pos, port = best_removal
                ports_to_remove.append((route_idx, pos, port))
                valid_positions.remove((route_idx, pos))
            else:
                break

        # Sort removals in reverse order to avoid index issues
        ports_to_remove.sort(key=lambda x: (x[0], x[1]), reverse=True)
        removed_ports = []
        routes_to_remove = []

        # Apply removals
        for route_idx, pos, port in ports_to_remove:
            if route_idx < len(solution.routes):
                route = solution.routes[route_idx]
                if pos < len(route) and route[pos] == port:
                    route.pop(pos)
                    removed_ports.append(port)

                    # Update port visits
                    if port in solution.port_visits and solution.port_visits[port]['visit_days']:
                        solution.port_visits[port]['visit_days'].pop()
                        solution.port_visits[port]['deliveries'].pop()
                        solution.port_visits[port]['vessel_types'].pop()

                    if port not in solution.unvisited_ports:
                        solution.unvisited_ports.append(port)

                    # Mark empty routes for removal
                    if len(route) <= 1:
                        routes_to_remove.append(route_idx)

        # Remove empty routes in reverse order
        for route_idx in sorted(set(routes_to_remove), reverse=True):
            if route_idx < len(solution.routes):
                solution.routes.pop(route_idx)
                solution.vessel_types.pop(route_idx)
                solution.arrival_times.pop(route_idx)
                if len(solution.departure_times) > route_idx:
                    solution.departure_times.pop(route_idx)
                if len(solution.loading_amounts) > route_idx:
                    solution.loading_amounts.pop(route_idx)

        return removed_ports

    def _calculate_relatedness(self, port1: int, port2: int, cycle: int = 0) -> float:
        """Calculate relatedness based on distance and demand variance."""
        max_distance = max(max(row) for row in self.problem.vessel_types[0]["travel_cost"])
        distance = self.problem.vessel_types[0]["travel_cost"][port1][port2]
        distance_score = 1 - (distance / max_distance) if max_distance > 0 else 0

        variance1 = self.problem.ports[port1]["sigma_demand"][0]  # Use first day
        variance2 = self.problem.ports[port2]["sigma_demand"][0]  # Use first day
        max_variance = max(p["sigma_demand"][0] for p in self.problem.ports[1:])
        variance_diff = abs(variance1 - variance2)
        variance_score = 1 - (variance_diff / max_variance) if max_variance > 0 else 0

        return 0.5 * distance_score + 0.5 * variance_score


class WorstRemoval(RemovalOperator):
    """Removal operator based on highest cost impact."""

    def apply(self, solution: Solution, cycle: int = 0, removal_count: int = 1) -> List[int]:
        """Remove ports that contribute most to expected cost."""
        removable_positions = self.get_removable_positions(solution)
        if not removable_positions:
            return []

        # Validate positions
        valid_positions = []
        for route_idx, pos in removable_positions:
            if route_idx < len(solution.routes):
                route = solution.routes[route_idx]
                if pos < len(route) and route[pos] != 0:
                    valid_positions.append((route_idx, pos))

        if not valid_positions:
            return []

        # Calculate cost contribution for each port
        cost_impacts = []
        original_cost = solution.calculate_cost()

        for route_idx, pos in valid_positions:
            route = solution.routes[route_idx]
            port = route[pos]

            # Skip if this is the supply port
            if port == 0:
                continue

            # Create a deep copy of the solution to avoid side effects
            temp_solution = copy.deepcopy(solution)
            temp_route = temp_solution.routes[route_idx]

            # Temporarily remove port
            temp_route.pop(pos)
            if port in temp_solution.port_visits and temp_solution.port_visits[port]['visit_days']:
                temp_solution.port_visits[port]['visit_days'].pop()
                temp_solution.port_visits[port]['deliveries'].pop()
                temp_solution.port_visits[port]['vessel_types'].pop()

            # Calculate new cost
            new_cost = temp_solution.calculate_cost()
            cost_impact = original_cost - new_cost

            cost_impacts.append((route_idx, pos, port, cost_impact))

        # Sort by cost impact (highest first - these are the ports to remove)
        cost_impacts.sort(key=lambda x: x[3], reverse=True)

        # Limit to removal count
        ports_to_remove = cost_impacts[:min(removal_count, len(cost_impacts))]

        # Sort removals in reverse order to avoid index issues
        ports_to_remove.sort(key=lambda x: (x[0], x[1]), reverse=True)
        removed_ports = []
        routes_to_remove = []

        # Apply removals
        for route_idx, pos, port, _ in ports_to_remove:
            if route_idx < len(solution.routes):
                route = solution.routes[route_idx]
                if pos < len(route) and route[pos] == port:
                    route.pop(pos)
                    removed_ports.append(port)

                    # Update port visits
                    if port in solution.port_visits and solution.port_visits[port]['visit_days']:
                        solution.port_visits[port]['visit_days'].pop()
                        solution.port_visits[port]['deliveries'].pop()
                        solution.port_visits[port]['vessel_types'].pop()

                    if port not in solution.unvisited_ports:
                        solution.unvisited_ports.append(port)

                    # Mark empty routes for removal
                    if len(route) <= 1:
                        routes_to_remove.append(route_idx)

        # Remove empty routes in reverse order
        for route_idx in sorted(set(routes_to_remove), reverse=True):
            if route_idx < len(solution.routes):
                solution.routes.pop(route_idx)
                solution.vessel_types.pop(route_idx)
                solution.arrival_times.pop(route_idx)
                if len(solution.departure_times) > route_idx:
                    solution.departure_times.pop(route_idx)
                if len(solution.loading_amounts) > route_idx:
                    solution.loading_amounts.pop(route_idx)

        return removed_ports


class VarianceBasedRemoval(RemovalOperator):
    """Removal operator based on demand variance."""

    def apply(self, solution: Solution, cycle: int = 0, removal_count: int = 1) -> List[int]:
        """Remove ports with highest demand variance."""
        removable_positions = self.get_removable_positions(solution, cycle)
        if not removable_positions:
            return []

        variance_scores = []
        for route_idx, pos in removable_positions:
            port = solution.routes[route_idx][pos]
            variance = self.problem.ports[port]["sigma_demand"][0]  # Use first day
            variance_scores.append((route_idx, pos, port, variance))

        variance_scores.sort(key=lambda x: x[3], reverse=True)
        removed_ports = []

        # Create a copy of routes to avoid index errors
        routes_to_remove = []
        ports_to_remove = []

        # Process removal candidates
        for i, (route_idx, pos, port, _) in enumerate(variance_scores):
            # Skip if we've already removed enough ports
            if len(ports_to_remove) >= removal_count:
                break

            # Get the route
            if route_idx >= len(solution.routes):
                continue

            route = solution.routes[route_idx]

            # Skip if position is invalid
            if pos >= len(route):
                continue

            # Skip if this is the supply port
            if route[pos] == 0:
                continue

            # Add to removal list
            ports_to_remove.append((route_idx, pos, port))

        # Apply removals in reverse order of route_idx and pos to avoid index issues
        ports_to_remove.sort(key=lambda x: (x[0], x[1]), reverse=True)

        for route_idx, pos, port in ports_to_remove:
            # Remove the port from the route
            route = solution.routes[route_idx]
            route.pop(pos)

            # Update port visits if there are any
            if port in solution.port_visits and solution.port_visits[port]['visit_days']:
                solution.port_visits[port]['visit_days'].pop()
                solution.port_visits[port]['deliveries'].pop()
                solution.port_visits[port]['vessel_types'].pop()

            removed_ports.append(port)

            # Add port back to unvisited list
            if port not in solution.unvisited_ports:
                solution.unvisited_ports.append(port)

            # If route is now just [0] or [0, 0], mark for removal
            if len(route) <= 2 and all(p == 0 for p in route):
                routes_to_remove.append(route_idx)

        # Remove empty routes in reverse order to avoid index issues
        for route_idx in sorted(set(routes_to_remove), reverse=True):
            if route_idx < len(solution.routes):
                solution.routes.pop(route_idx)
                solution.vessel_types.pop(route_idx)
                solution.arrival_times.pop(route_idx)
                if len(solution.departure_times) > route_idx:
                    solution.departure_times.pop(route_idx)
                if len(solution.loading_amounts) > route_idx:
                    solution.loading_amounts.pop(route_idx)

        return removed_ports


class InsertionOperator:
    """Base class for insertion operators."""

    def __init__(self, problem):
        self.problem = problem

    def validate_insertion(self, solution: Solution, cycle: int, route_idx: int,
                           pos: int, port: int, vessel_type: int) -> bool:
        """Check if inserting a port into a route is feasible."""
        route = solution.routes[route_idx]
        new_route = route[:pos] + [port] + route[pos:]
        return solution.validate_route(new_route, vessel_type)


class GreedyInsertion(InsertionOperator):
    """Greedy insertion operator."""

    def apply(self, solution: Solution, cycle: int = 0):
        """Insert unvisited ports to minimize expected cost increase."""
        while solution.unvisited_ports:
            best_insertion = None
            best_cost = float('inf')

            for port in solution.unvisited_ports:
                # Try existing routes
                for route_idx, route in enumerate(solution.routes):
                    vessel_type = solution.vessel_types[route_idx]
                    for pos in range(len(route)):
                        if self.validate_insertion(solution, cycle, route_idx, pos, port, vessel_type):
                            cost = self._calculate_insertion_cost(solution, cycle, route_idx, pos, port, vessel_type)
                            if cost < best_cost:
                                best_cost = cost
                                best_insertion = (port, route_idx, pos, vessel_type)

                # Try new route
                for vessel_type in range(len(self.problem.vessel_types)):
                    new_route = [0, port, 0]
                    if solution.validate_route(new_route, vessel_type):
                        cost = self._calculate_new_route_cost(solution, cycle, new_route, vessel_type)
                        if cost < best_cost:
                            best_cost = cost
                            best_insertion = (port, -1, 0, vessel_type)

            if best_insertion:
                self._apply_insertion(solution, cycle, best_insertion)
            else:
                break

    def _calculate_insertion_cost(self, solution: Solution, cycle: int, route_idx: int,
                                  pos: int, port: int, vessel_type: int) -> float:
        """Calculate expected cost increase of inserting a port."""
        route = solution.routes[route_idx]
        original_cost = solution.calculate_cost()

        route.insert(pos, port)
        solution.arrival_times[route_idx] = solution.calculate_arrival_times(
            route, vessel_type)
        solution.port_visits[port]['visit_days'].append(0)
        solution.port_visits[port]['deliveries'].append(
            self.problem.safety_stock[port - 1][0])  # Use first day
        solution.port_visits[port]['vessel_types'].append(vessel_type)

        new_cost = solution.calculate_cost()

        route.pop(pos)
        solution.port_visits[port]['visit_days'].pop()
        solution.port_visits[port]['deliveries'].pop()
        solution.port_visits[port]['vessel_types'].pop()

        return new_cost - original_cost

    def _calculate_new_route_cost(self, solution: Solution, cycle: int = 0,
                                  new_route: List[int] = None, vessel_type: int = 0) -> float:
        """Calculate expected cost of creating a new route."""
        original_cost = solution.calculate_cost()

        solution.routes.append(new_route)
        solution.vessel_types.append(vessel_type)
        solution.arrival_times.append(
            solution.calculate_arrival_times(new_route, vessel_type))
        port = new_route[1]
        solution.port_visits[port]['visit_days'].append(0)
        solution.port_visits[port]['deliveries'].append(
            self.problem.safety_stock[port - 1][0])  # Use first day
        solution.port_visits[port]['vessel_types'].append(vessel_type)

        new_cost = solution.calculate_cost()

        solution.routes.pop()
        solution.vessel_types.pop()
        solution.arrival_times.pop()
        solution.port_visits[port]['visit_days'].pop()
        solution.port_visits[port]['deliveries'].pop()
        solution.port_visits[port]['vessel_types'].pop()

        return new_cost - original_cost

    def _apply_insertion(self, solution: Solution, cycle: int = 0, insertion: Tuple[int, int, int, int] = None):
        """Apply the selected insertion."""
        port, route_idx, pos, vessel_type = insertion

        if route_idx == -1:
            new_route = [0, port, 0]
            solution.routes.append(new_route)
            solution.vessel_types.append(vessel_type)
            solution.arrival_times.append(
                solution.calculate_arrival_times(new_route, vessel_type))
        else:
            route = solution.routes[route_idx]
            route.insert(pos, port)
            solution.arrival_times[route_idx] = solution.calculate_arrival_times(
                route, vessel_type)

        solution.port_visits[port]['visit_days'].append(0)
        solution.port_visits[port]['deliveries'].append(
            self.problem.safety_stock[port - 1][0])  # Use first day
        solution.port_visits[port]['vessel_types'].append(vessel_type)
        solution.unvisited_ports.remove(port)


class InventorySensitiveInsertion(InsertionOperator):
    """Insertion operator prioritizing ports with expected inventory shortages."""

    def apply(self, solution: Solution, cycle: int = 0):
        """Insert ports based on expected inventory shortages."""
        while solution.unvisited_ports:
            best_insertion = None
            best_score = float('-inf')

            for port in solution.unvisited_ports:
                shortage_score = self._calculate_shortage_score(port, solution)

                # Try existing routes
                for route_idx, route in enumerate(solution.routes):
                    vessel_type = solution.vessel_types[route_idx]
                    for pos in range(len(route)):
                        if self.validate_insertion(solution, cycle, route_idx, pos, port, vessel_type):
                            cost = self._calculate_insertion_cost(solution, cycle, route_idx, pos, port, vessel_type)
                            score = shortage_score / (cost + 1)  # Balance shortage urgency and cost
                            if score > best_score:
                                best_score = score
                                best_insertion = (port, route_idx, pos, vessel_type)

                # Try new route
                for vessel_type in range(len(self.problem.vessel_types)):
                    new_route = [0, port, 0]
                    if solution.validate_route(new_route, vessel_type):
                        cost = self._calculate_new_route_cost(solution, cycle, new_route, vessel_type)
                        score = shortage_score / (cost + 1)
                        if score > best_score:
                            best_score = score
                            best_insertion = (port, -1, 0, vessel_type)

            if best_insertion:
                self._apply_insertion(solution, cycle, best_insertion)
            else:
                break

    def _calculate_shortage_score(self, port: int, solution: Solution) -> float:
        """Calculate score based on expected inventory shortage."""
        port_info = self.problem.ports[port]
        expected_inventory = solution.get_final_inventory(port) - port_info["mu_demand"][0]  # Use first day
        lower_limit = port_info["inventory_lower_limit"]
        return max(0, lower_limit - expected_inventory)  # Higher score for larger shortages

    def _calculate_insertion_cost(self, solution: Solution, cycle: int, route_idx: int,
                                  pos: int, port: int, vessel_type: int) -> float:
        """Same as GreedyInsertion."""
        return GreedyInsertion(self.problem)._calculate_insertion_cost(
            solution, cycle, route_idx, pos, port, vessel_type)

    def _calculate_new_route_cost(self, solution: Solution, cycle: int,
                                  new_route: List[int], vessel_type: int) -> float:
        """Same as GreedyInsertion."""
        return GreedyInsertion(self.problem)._calculate_new_route_cost(
            solution, cycle, new_route, vessel_type)

    def _apply_insertion(self, solution: Solution, cycle: int, insertion: Tuple[int, int, int, int]):
        """Same as GreedyInsertion."""
        GreedyInsertion(self.problem)._apply_insertion(solution, cycle, insertion)


class RegretInsertion(InsertionOperator):
    """Regret insertion operator that prioritizes ports with high regret value."""

    def __init__(self, problem, k=2):
        """Initialize with regret parameter k (default: 2)."""
        super().__init__(problem)
        self.k = k  # Number of best positions to consider for regret

    def apply(self, solution: Solution, cycle: int = 0):
        """Insert unvisited ports based on regret value."""
        while solution.unvisited_ports:
            # Calculate regret values for all unvisited ports
            port_regrets = []

            for port in solution.unvisited_ports:
                # Find all feasible insertion positions and their costs
                insertion_costs = []
                insertion_positions = []

                # Try existing routes
                for route_idx, route in enumerate(solution.routes):
                    vessel_type = solution.vessel_types[route_idx]
                    for pos in range(len(route)):
                        if self.validate_insertion(solution, cycle, route_idx, pos, port, vessel_type):
                            cost = self._calculate_insertion_cost(solution, cycle, route_idx, pos, port, vessel_type)
                            insertion_costs.append(cost)
                            insertion_positions.append((port, route_idx, pos, vessel_type))

                # Try new routes
                for vessel_type in range(len(self.problem.vessel_types)):
                    new_route = [0, port, 0]
                    if solution.validate_route(new_route, vessel_type):
                        cost = self._calculate_new_route_cost(solution, cycle, new_route, vessel_type)
                        insertion_costs.append(cost)
                        insertion_positions.append((port, -1, 0, vessel_type))

                # Calculate regret value if we have enough insertion options
                if len(insertion_costs) >= 1:
                    # Sort costs in ascending order
                    sorted_costs = sorted(insertion_costs)
                    best_cost = sorted_costs[0]
                    best_position = insertion_positions[insertion_costs.index(best_cost)]

                    # Calculate regret value (sum of differences between best and k-best insertions)
                    regret_value = 0
                    for i in range(1, min(self.k, len(sorted_costs))):
                        regret_value += sorted_costs[i] - best_cost

                    # If we have fewer than k options, add a penalty
                    if len(sorted_costs) < self.k:
                        # Add a large value for each missing option
                        regret_value += (self.k - len(sorted_costs)) * 10000

                    port_regrets.append((port, regret_value, best_position, best_cost))

            if not port_regrets:
                break  # No feasible insertions found

            # Select port with highest regret value
            port_regrets.sort(key=lambda x: (-x[1], x[3]))  # Sort by regret (desc) and cost (asc)
            _, _, best_insertion, _ = port_regrets[0]

            # Apply the insertion
            self._apply_insertion(solution, cycle, best_insertion)

    def _calculate_insertion_cost(self, solution: Solution, cycle: int, route_idx: int,
                                 pos: int, port: int, vessel_type: int) -> float:
        """Same as GreedyInsertion."""
        return GreedyInsertion(self.problem)._calculate_insertion_cost(
            solution, cycle, route_idx, pos, port, vessel_type)

    def _calculate_new_route_cost(self, solution: Solution, cycle: int,
                                 new_route: List[int], vessel_type: int) -> float:
        """Same as GreedyInsertion."""
        return GreedyInsertion(self.problem)._calculate_new_route_cost(
            solution, cycle, new_route, vessel_type)

    def _apply_insertion(self, solution: Solution, cycle: int, insertion: Tuple[int, int, int, int]):
        """Same as GreedyInsertion."""
        GreedyInsertion(self.problem)._apply_insertion(solution, cycle, insertion)


class RandomInsertion(InsertionOperator):
    """Random insertion operator that inserts ports at random feasible positions."""

    def apply(self, solution: Solution, cycle: int = 0):
        """Insert unvisited ports at random feasible positions."""
        # Create a copy of unvisited ports to avoid modifying during iteration
        unvisited_ports = solution.unvisited_ports.copy()
        random.shuffle(unvisited_ports)  # Randomize the order

        for port in unvisited_ports:
            if port not in solution.unvisited_ports:
                continue  # Port may have been inserted in a previous iteration

            # Find all feasible insertion positions
            feasible_insertions = []

            # Try existing routes
            for route_idx, route in enumerate(solution.routes):
                vessel_type = solution.vessel_types[route_idx]
                for pos in range(len(route)):
                    if self.validate_insertion(solution, cycle, route_idx, pos, port, vessel_type):
                        feasible_insertions.append((port, route_idx, pos, vessel_type))

            # Try new routes
            for vessel_type in range(len(self.problem.vessel_types)):
                new_route = [0, port, 0]
                if solution.validate_route(new_route, vessel_type):
                    feasible_insertions.append((port, -1, 0, vessel_type))

            # If feasible positions exist, choose one randomly
            if feasible_insertions:
                insertion = random.choice(feasible_insertions)
                self._apply_insertion(solution, cycle, insertion)

    def _apply_insertion(self, solution: Solution, cycle: int, insertion: Tuple[int, int, int, int]):
        """Same as GreedyInsertion."""
        GreedyInsertion(self.problem)._apply_insertion(solution, cycle, insertion)


def generate_initial_solution(problem) -> Solution:
    """Generate an initial solution using a Feasibility First strategy.
    Ensures all necessary port visits are feasibly scheduled within 25 days.
    Respects the limit of maximum 2 visits per port with different vessel types.
    Uses only 2 vessels (one of each type).
    """
    solution = Solution(problem)

    # Solution already has one vessel of each type from the Solution.__init__ method
    # We don't need to add any more vessels

    # Simulate inventory for each port assuming no deliveries to identify potential violations
    port_violations = []
    for port in range(1, problem.N):
        port_info = problem.ports[port]
        inventory = port_info["initial_inventory"]
        first_violation_day = None

        # Calculate confidence interval parameters for constraint checking
        rh = 1.0 - problem.confidence_level  # e.g., 0.1
        alpha_L = rh / 2.0  # Violation probability for lower bound
        alpha_U = rh / 2.0  # Violation probability for upper bound
        tau = norm.ppf(alpha_L)  # tauZ-score for lower bound (negative value)
        zeta = norm.ppf(1.0 - alpha_U)  # Z-score for upper bound (positive value)

        # Simulate inventory for the entire planning period
        for day in range(problem.T):
            # Subtract expected demand
            inventory -= port_info["mu_demand"][day]

            # Check lower bound violation
            lower_limit = port_info["inventory_lower_limit"]
            sigma_demand = port_info["sigma_demand"][day]
            lower_bound_check = lower_limit - tau * sigma_demand  # Note: -tau is positive

            if inventory < lower_bound_check and first_violation_day is None:
                first_violation_day = day
                break

        port_violations.append({
            'port': port,
            'first_violation_day': first_violation_day if first_violation_day is not None else float('inf'),
            'initial_inventory': port_info["initial_inventory"],
            'lower_limit': port_info["inventory_lower_limit"],
            'upper_limit': port_info["inventory_upper_limit"]
        })

    # Sort ports by urgency (earliest violation day first)
    port_violations.sort(key=lambda x: x['first_violation_day'])

    # Initialize tracking for port visits
    ports_visited_once = set()
    ports_visited_twice = set()
    ports_needing_visit = list(range(1, problem.N))

    # Process ports in order of urgency
    while ports_needing_visit:
        # Find the most urgent port
        urgent_port = None
        for port in ports_needing_visit:
            if port not in ports_visited_twice:  # Can still be visited
                urgent_port = port
                break

        if urgent_port is None:
            break  # No more ports to visit

        # Find the best feasible insertion for this port
        best_insertion = None
        best_cost = float('inf')

        # Try each vessel type
        for vessel_type in range(len(problem.vessel_types)):
            # Skip if port already visited by this vessel type
            if urgent_port in ports_visited_once and vessel_type in solution.port_visits[urgent_port]['vessel_types']:
                continue

            # Try creating a new route for this port
            route = [0, urgent_port, 0]  # Simple route: supply -> port -> supply

            # Create a temporary copy of the solution
            temp_solution = copy.deepcopy(solution)

            # Try to add the route
            if temp_solution.add_route(route, vessel_type):
                # Calculate cost
                cost = temp_solution.calculate_cost()

                # Check if constraints are satisfied
                constraints_satisfied = True
                if hasattr(temp_solution, 'inventory_mean'):
                    # Use the correct constraint checking logic
                    rh = 1.0 - problem.confidence_level
                    alpha_L = rh / 2.0
                    alpha_U = rh / 2.0
                    tau = norm.ppf(alpha_L)
                    zeta = norm.ppf(1.0 - alpha_U)

                    for p in range(1, problem.N):
                        if p in temp_solution.inventory_mean:
                            p_info = problem.ports[p]
                            lower_limit = p_info['inventory_lower_limit']
                            upper_limit = p_info['inventory_upper_limit']

                            for d in range(1, min(len(temp_solution.inventory_mean[p]), problem.T + 1)):
                                inventory_mean = temp_solution.inventory_mean[p][d]
                                sigma_demand = p_info['sigma_demand'][d-1]

                                # Check lower bound violation
                                lower_bound_check = lower_limit - tau * sigma_demand
                                if inventory_mean < lower_bound_check:
                                    constraints_satisfied = False
                                    break

                                # Check upper bound violation
                                upper_bound_check = upper_limit - zeta * sigma_demand
                                if inventory_mean > upper_bound_check:
                                    constraints_satisfied = False
                                    break

                if constraints_satisfied and cost < best_cost:
                    best_cost = cost
                    best_insertion = (route, vessel_type)

        if best_insertion:
            # Apply the best insertion to the actual solution
            route, vessel_type = best_insertion
            solution.add_route(route, vessel_type)

            # Update visit tracking
            if urgent_port in ports_visited_once:
                ports_visited_twice.add(urgent_port)
            else:
                ports_visited_once.add(urgent_port)

            # Remove port from needing visit if visited twice
            if urgent_port in ports_visited_twice:
                ports_needing_visit.remove(urgent_port)
        else:
            # If no feasible insertion found, move to the next port
            print(f"Warning: No feasible insertion found for port {urgent_port}")
            ports_needing_visit.remove(urgent_port)

    # Verify all ports are visited at least once
    visited_ports = set()
    for vessel in solution.vessels:
        for event in vessel['schedule']:
            if event['port'] != 0 and event['load_change'] < 0:  # Unloading event at consumption port
                visited_ports.add(event['port'])

    missing_ports = set(range(1, problem.N)) - visited_ports
    if missing_ports:
        print(f"Warning: Ports {missing_ports} could not be feasibly visited")

    return solution


def apply_2opt(solution: Solution) -> bool:
    """
    Apply 2-opt local search to improve routes by reversing segments.
    Returns True if an improvement was made, False otherwise.
    """
    improved = False

    # Iterate through each vessel with a non-empty schedule
    for vessel_idx, vessel in enumerate(solution.vessels):
        if not vessel['schedule']:
            continue

        # Extract the route from the schedule (only consumption ports)
        route = []
        route_indices = []  # To map back to original schedule indices
        for i, event in enumerate(vessel['schedule']):
            if event['port'] != 0 and event['load_change'] < 0:  # Unloading event at consumption port
                route.append(event['port'])
                route_indices.append(i)

        if len(route) < 2:  # Need at least 2 ports for 2-opt
            continue

        vessel_type = vessel['type']
        travel_costs = solution.problem.vessel_types[vessel_type]['travel_cost']

        # Try all possible 2-opt swaps
        for i in range(len(route) - 1):
            for j in range(i + 1, len(route) - 1):
                # Calculate cost change for reversing segment route[i+1:j+1]
                # Current edges: (route[i], route[i+1]) and (route[j], route[j+1])
                # New edges after reversal: (route[i], route[j]) and (route[i+1], route[j+1])
                current_cost = travel_costs[route[i]][route[i+1]] + travel_costs[route[j]][route[j+1]]
                new_cost = travel_costs[route[i]][route[j]] + travel_costs[route[i+1]][route[j+1]]

                if new_cost < current_cost:
                    # Create a deep copy of the solution to test the change
                    test_solution = copy.deepcopy(solution)
                    test_vessel = test_solution.vessels[vessel_idx]

                    # Reverse the segment in the schedule
                    segment_indices = route_indices[i+1:j+1]
                    segment = [test_vessel['schedule'][idx] for idx in segment_indices]
                    segment.reverse()

                    for k, idx in enumerate(segment_indices):
                        test_vessel['schedule'][idx] = segment[k]

                    # Recalculate arrival times and update schedule
                    current_time = 0
                    current_location = 0

                    for event_idx, event in enumerate(test_vessel['schedule']):
                        port = event['port']

                        # Calculate travel time from previous location
                        if event_idx > 0 and current_location != port:
                            travel_time = solution.problem.vessel_types[vessel_type]['travel_time'][current_location][port]
                            arrival_time = current_time + travel_time
                        else:
                            arrival_time = current_time

                        # Update event arrival time
                        event['arrival_time'] = arrival_time

                        # Update current state
                        current_time = arrival_time + 1  # 1 day for loading/unloading
                        current_location = port

                    # Calculate new solution cost
                    test_solution.calculate_cost()  # This updates inventory_mean

                    # Check if the new solution satisfies inventory constraints
                    constraints_satisfied = True
                    if hasattr(test_solution, 'inventory_mean'):
                        # Calculate confidence interval parameters
                        rh = 1.0 - test_solution.problem.confidence_level  # e.g., 0.1
                        alpha_L = rh / 2.0  # Violation probability for lower bound
                        alpha_U = rh / 2.0  # Violation probability for upper bound
                        tau = norm.ppf(alpha_L)  # Should be negative, e.g., -1.645
                        zeta = norm.ppf(1.0 - alpha_U)  # Should be positive, e.g., +1.645

                        for port in range(1, test_solution.problem.N):
                            if port in test_solution.inventory_mean:
                                port_info = test_solution.problem.ports[port]
                                lower_limit = port_info['inventory_lower_limit']
                                upper_limit = port_info['inventory_upper_limit']

                                for day in range(1, min(len(test_solution.inventory_mean[port]), test_solution.problem.T + 1)):
                                    inventory_mean = test_solution.inventory_mean[port][day]
                                    sigma_demand = port_info['sigma_demand'][day-1]

                                    # S&A Eq 27 check: Mh >= SLm - tau * sigma
                                    lower_bound_check = lower_limit - tau * sigma_demand  # Note: -tau is positive
                                    if inventory_mean < lower_bound_check:
                                        constraints_satisfied = False
                                        break

                                    # S&A Eq 28 check: Mh <= SLM - zeta * sigma
                                    upper_bound_check = upper_limit - zeta * sigma_demand  # Note: -zeta is negative
                                    if inventory_mean > upper_bound_check:
                                        constraints_satisfied = False
                                        break

                    if constraints_satisfied and test_solution.total_cost < solution.total_cost:
                        # Apply the change to the original solution
                        solution.vessels[vessel_idx] = test_vessel
                        solution.total_cost = test_solution.total_cost
                        solution.inventory_mean = test_solution.inventory_mean
                        improved = True
                        break

            if improved:
                break  # Break out of the outer loop to restart with the new route

    return improved


def apply_swap_1_1(solution: Solution) -> bool:
    """
    Apply swap local search to improve routes by swapping single ports between routes.
    Returns True if an improvement was made, False otherwise.
    """
    improved = False

    # Need at least 2 vessels with schedules
    vessels_with_schedules = [v for v in solution.vessels if v['schedule']]
    if len(vessels_with_schedules) < 2:
        return False

    # Iterate through all pairs of vessels
    for i in range(len(solution.vessels)):
        vessel1 = solution.vessels[i]
        if not vessel1['schedule']:
            continue

        vessel1_type = vessel1['type']

        for j in range(i + 1, len(solution.vessels)):
            vessel2 = solution.vessels[j]
            if not vessel2['schedule']:
                continue

            vessel2_type = vessel2['type']

            # Find all consumption port events in each vessel's schedule
            consumption_events1 = [(idx, event) for idx, event in enumerate(vessel1['schedule'])
                                 if event['port'] != 0 and event['load_change'] < 0]
            consumption_events2 = [(idx, event) for idx, event in enumerate(vessel2['schedule'])
                                 if event['port'] != 0 and event['load_change'] < 0]

            if not consumption_events1 or not consumption_events2:
                continue  # Skip if either vessel has no consumption ports

            # Try all possible port swaps
            for idx1, event1 in consumption_events1:
                port1 = event1['port']

                for idx2, event2 in consumption_events2:
                    port2 = event2['port']

                    # Create a deep copy of the solution to test the swap
                    test_solution = copy.deepcopy(solution)
                    test_vessel1 = test_solution.vessels[i]
                    test_vessel2 = test_solution.vessels[j]

                    # Swap the ports
                    test_vessel1['schedule'][idx1]['port'] = port2
                    test_vessel2['schedule'][idx2]['port'] = port1

                    # Recalculate arrival times and update schedules for both vessels
                    for vessel_idx, vessel in [(i, test_vessel1), (j, test_vessel2)]:
                        current_time = 0
                        current_location = 0

                        for event_idx, event in enumerate(vessel['schedule']):
                            port = event['port']

                            # Calculate travel time from previous location
                            if event_idx > 0 and current_location != port:
                                vessel_type = vessel['type']
                                travel_time = solution.problem.vessel_types[vessel_type]['travel_time'][current_location][port]
                                arrival_time = current_time + travel_time
                            else:
                                arrival_time = current_time

                            # Update event arrival time
                            event['arrival_time'] = arrival_time

                            # Update current state
                            current_time = arrival_time + 1  # 1 day for loading/unloading
                            current_location = port

                    # Calculate new solution cost
                    test_solution.calculate_cost()  # This updates inventory_mean

                    # Check if the new solution satisfies inventory constraints
                    constraints_satisfied = True
                    if hasattr(test_solution, 'inventory_mean'):
                        # Calculate confidence interval parameters
                        rh = 1.0 - test_solution.problem.confidence_level  # e.g., 0.1
                        alpha_L = rh / 2.0  # Violation probability for lower bound
                        alpha_U = rh / 2.0  # Violation probability for upper bound
                        tau = norm.ppf(alpha_L)  # Should be negative, e.g., -1.645
                        zeta = norm.ppf(1.0 - alpha_U)  # Should be positive, e.g., +1.645

                        for port in range(1, test_solution.problem.N):
                            if port in test_solution.inventory_mean:
                                port_info = test_solution.problem.ports[port]
                                lower_limit = port_info['inventory_lower_limit']
                                upper_limit = port_info['inventory_upper_limit']

                                for day in range(1, min(len(test_solution.inventory_mean[port]), test_solution.problem.T + 1)):
                                    inventory_mean = test_solution.inventory_mean[port][day]
                                    sigma_demand = port_info['sigma_demand'][day-1]

                                    # S&A Eq 27 check: Mh >= SLm - tau * sigma
                                    lower_bound_check = lower_limit - tau * sigma_demand  # Note: -tau is positive
                                    if inventory_mean < lower_bound_check:
                                        constraints_satisfied = False
                                        break

                                    # S&A Eq 28 check: Mh <= SLM - zeta * sigma
                                    upper_bound_check = upper_limit - zeta * sigma_demand  # Note: -zeta is negative
                                    if inventory_mean > upper_bound_check:
                                        constraints_satisfied = False
                                        break

                    if constraints_satisfied and test_solution.total_cost < solution.total_cost:
                        # Apply the swap to the original solution
                        solution.vessels[i] = test_vessel1
                        solution.vessels[j] = test_vessel2
                        solution.total_cost = test_solution.total_cost
                        solution.inventory_mean = test_solution.inventory_mean
                        improved = True
                        break

                if improved:
                    break  # Break out of the inner loop

            if improved:
                break  # Break out of the vessel2 loop

        if improved:
            break  # Break out of the vessel1 loop

    return improved


def initialize_operators(problem):
    """Initialize all operators with their weights."""
    removal_operators = {
        'random': RandomRemoval(problem),
        'related': RelatedRemoval(problem),
        'worst': WorstRemoval(problem),
        'variance': VarianceBasedRemoval(problem)
    }

    insertion_operators = {
        'greedy': GreedyInsertion(problem),
        'inventory': InventorySensitiveInsertion(problem),
        'regret': RegretInsertion(problem, k=2),  # Regret-2 insertion
        'random': RandomInsertion(problem)  # Random insertion
    }

    operator_weights = {
        'removal': {name: 1.0 for name in removal_operators},
        'insertion': {name: 1.0 for name in insertion_operators}
    }

    return removal_operators, insertion_operators, operator_weights