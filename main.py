from base import Problem, Solution, generate_initial_solution
from ALNS import ALNSControll<PERSON>


def print_solution(solution: Solution):
    """Print the solution details showing complete vessel paths over the planning period."""
    print(f"Total Expected Cost: {solution.total_cost:.2f}")
    print("\nVessel Routes and Schedules over 25-day Planning Period:")

    # Print each vessel's complete route and schedule
    for vessel_idx, vessel in enumerate(solution.vessels):
        vessel_type = vessel['type']
        vessel_capacity = solution.problem.vessel_types[vessel_type]['capacity']
        print(f"\nVessel {vessel_idx + 1} (Type: {vessel_type}, Capacity: {vessel_capacity}):")

        if not vessel['schedule']:
            print("  No activities scheduled")
            continue

        # Print the complete path
        path = [event['port'] for event in vessel['schedule']]
        print(f"  Complete path: {path}")

        # Print detailed schedule
        print("  Detailed schedule:")
        current_load = 0
        for event in vessel['schedule']:
            port = event['port']
            arrival_time = event['arrival_time']
            departure_time = event['departure_time']
            load_change = event['load_change']

            if port == 0:  # Supply port
                if load_change > 0:
                    print(f"    Day {arrival_time:.1f}: Arrive at port {port} (supply port)")
                    print(f"    Day {arrival_time:.1f}-{departure_time:.1f}: Load {load_change:.0f} units")
                    print(f"    Day {departure_time:.1f}: Depart from port {port} with {current_load + load_change:.0f} units")
                    current_load += load_change
                else:
                    print(f"    Day {arrival_time:.1f}: Return to port {port} (supply port)")
            else:  # Consumption port
                print(f"    Day {arrival_time:.1f}: Arrive at port {port} with {current_load:.0f} units")
                print(f"    Day {arrival_time:.1f}-{departure_time:.1f}: Unload {-load_change:.0f} units")
                print(f"    Day {departure_time:.1f}: Depart from port {port} with {current_load + load_change:.0f} units")
                current_load += load_change


def validate_solution(solution: Solution):
    """Validate visit constraints and hard inventory constraints."""
    from scipy.stats import norm
    print("\nValidation Results:")
    valid = True

    # Check visit constraints (max 2 visits per port, different vessels)
    for port in range(1, solution.problem.N):
        # Count visits by vessel type using the vessel schedules
        visits_by_type = {}
        for vessel in solution.vessels:
            vessel_type = vessel['type']
            for event in vessel['schedule']:
                if event['port'] == port and event['load_change'] < 0:  # Unloading event
                    if vessel_type not in visits_by_type:
                        visits_by_type[vessel_type] = 0
                    visits_by_type[vessel_type] += 1

        total_visits = sum(visits_by_type.values())
        unique_vessel_types = len(visits_by_type)

        if total_visits > 2:
            print(f"  Error: Port {port} visited {total_visits} times (max 2 allowed)")
            valid = False
        if total_visits == 2 and unique_vessel_types < 2:
            print(f"  Error: Port {port} visited twice with same vessel type")
            valid = False

    # Check hard inventory constraints
    if hasattr(solution, 'inventory_mean'):
        # Calculate confidence interval parameters
        rh = 1.0 - solution.problem.confidence_level  # e.g., 0.1
        alpha_L = rh / 2.0  # Violation probability for lower bound
        alpha_U = rh / 2.0  # Violation probability for upper bound
        # Z-score for lower bound (P(I < L) <= alpha_L)
        tau = norm.ppf(alpha_L)  # Should be negative, e.g., -1.645
        # Z-score for upper bound (P(I > U) <= alpha_U => P(I <= U) >= 1-alpha_U)
        zeta = norm.ppf(1.0 - alpha_U)  # Should be positive, e.g., +1.645

        # Check each port and day for constraint violations
        for port in range(1, solution.problem.N):
            if port in solution.inventory_mean:
                port_info = solution.problem.ports[port]
                lower_limit = port_info['inventory_lower_limit']
                upper_limit = port_info['inventory_upper_limit']

                for day in range(1, min(len(solution.inventory_mean[port]), solution.problem.T + 1)):
                    inventory_mean = solution.inventory_mean[port][day]
                    sigma_demand = port_info['sigma_demand'][day-1]  # Use demand sigma for the period *leading* to this inventory state

                    # S&A Eq 27 check: Mh >= SLm - tau * sigma
                    lower_bound_check = lower_limit - tau * sigma_demand  # Note: -tau is positive
                    if inventory_mean < lower_bound_check:
                        print(f"  Error: Port {port}, day {day} expected inventory {inventory_mean:.2f} violates lower limit {lower_bound_check:.2f}")
                        valid = False

                    # S&A Eq 28 check: Mh <= SLM - zeta * sigma
                    upper_bound_check = upper_limit - zeta * sigma_demand  # Note: -zeta is negative
                    if inventory_mean > upper_bound_check:
                        print(f"  Error: Port {port}, day {day} expected inventory {inventory_mean:.2f} violates upper limit {upper_bound_check:.2f}")
                        valid = False
    else:
        print("  Warning: No inventory mean data available for constraint validation")

    if valid:
        print("  All constraints satisfied!")
    else:
        print("  Validation failed.")


def main():
    """Main function to test the stochastic IRP solver."""
    # Configuration for ALNS
    config = {
        "iterations": 1000,
        "start_temperature": 1000.0,
        "cooling_rate": 0.995,
        "min_temperature": 0.1,
        "removal_count_min": 1,
        "removal_count_max": 3,
        "weight_update_frequency": 100,
        "weight_decay": 0.9,
        "acceptance_scores": [33, 22, 11, 0]
    }

    # Initialize problem
    problem = Problem(T=25)

    # Generate initial solution
    initial_solution = generate_initial_solution(problem)
    print("Initial solution:")
    print_solution(initial_solution)

    # Initialize and run ALNS
    alns = ALNSController(problem, initial_solution, config)
    best_solution = alns.run()

    # Output results
    print("Stochastic Inventory Routing Problem Solution")
    print("=" * 50)
    print_solution(best_solution)
    validate_solution(best_solution)


if __name__ == "__main__":
    main()

